package com.io661.extension.controller;

import com.io661.extension.commonURL.CommonYouPinHttpUrl;
import com.io661.extension.model.Enum.PlatEnum;
import com.io661.extension.model.Steam.*;
import com.io661.extension.model.YouPin.YouPinUserInventoryOnSellDataListRes;
import com.io661.extension.model.YouPin.YouPinUserItemsOffSaleReq;
import com.io661.extension.service.AccountManagerService;
import com.io661.extension.service.Impl.AccountManagerServiceImpl;
import com.io661.extension.service.Impl.TransactionAssistantServiceImpl;
import com.io661.extension.service.Impl.YouPinServiceImpl;
import com.io661.extension.service.TransactionAssistantService;
import com.io661.extension.service.YouPinService;
import com.io661.extension.util.YouPin.YouPinCookieManager;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.*;
import javafx.scene.shape.Circle;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import lombok.Data;

import java.math.BigDecimal;
import java.net.URL;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.io661.extension.util.YouPin.YouPinCookieManager.readYouPinCookie;

@Data
public class TransactionAssistantController implements Initializable {

    // 默认图片
    private final String DEFAULT_IMAGE_URL = "/com/io661/extension/img/Logo.png";
    @FXML
    public TextField searchField;
    @FXML
    public TextField onSaleSearchField;
    @FXML
    public Button uploadToMarketButton;
    public ComboBox<PlatEnum> onSellPlateSelector;
    private TransactionAssistantService transactionAssistantService;
    private AccountManagerService accountManagerService;
    private YouPinService youPinService;
    // Steam账号数据列表
    private ObservableList<SteamRes.SteamBind> steamAccounts = FXCollections.observableArrayList();
    // 当前选中的Steam账号
    private SteamRes.SteamBind selectedSteamAccount;
    // 库存数据
    private List<SteamInventoryRes> inventoryItems = new ArrayList<>();
    // 选中的物品列表
    private List<SteamInventoryRes> selectedItems = new ArrayList<>();
    // 当前库存查询参数
    private Integer currentType = 0;
    private String currentSteamId = "";
    private Integer currentLimit = 500;
    private Integer currentSubType = 0;
    private boolean currentOnSell = false;
    private Integer currentSort = 0;
    private String currentSearch = "";

    // 悠悠有品在售数据缓存
    private Map<String, List<YouPinUserInventoryOnSellDataListRes.CommodityInfoList>> youPinOnSellCache = new HashMap<>();
    private Map<String, Long> youPinCacheTimestamp = new HashMap<>();
    private static final long YOUPIN_CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

    @FXML
    private Button inventoryButton;

    @FXML
    private Button onSaleButton;

    @FXML
    private Button confirmButton;

    @FXML
    private Button priceButton;

    @FXML
    private StackPane inventoryPane;

    @FXML
    private StackPane onSalePane;

    @FXML
    private StackPane confirmPane;

    @FXML
    private StackPane pricePane;
    @FXML
    private StackPane loadingPane;

    @FXML
    private MenuButton platformSelector;

    @FXML
    private CheckBox selectAllCheckbox;

    @FXML
    private CheckBox onSaleSelectAllCheckbox;

    @FXML
    private Button unlistButton;

    @FXML
    private Button modifyPriceButton;

    @FXML
    private FlowPane inventoryItemsContainer;

    @FXML
    private FlowPane onSaleItemsContainer;

    @FXML
    private Pagination inventoryPagination;

    @FXML
    private Pagination onSalePagination;

    @FXML
    private Label usernameLabel;

    @FXML
    private Label balanceLabel;

    @FXML
    private Label tradeUrlLabel;

    @FXML
    private Label steamIdLabel;

    @FXML
    private Label inventorySummaryLabel;

    @FXML
    private ImageView avatarImageView;

    @FXML
    private Button refreshButton;

    @FXML
    private ComboBox<String> inventorySortSelector;

    @FXML
    private ComboBox<String> onSaleSortSelector;

    @FXML
    private Button mergeItemsButton;

    @FXML
    private Button unmergeItemsButton;

    @FXML
    private Label accountSelectorLabel;

    @FXML
    private VBox accountListContainer;

    @FXML
    private Button refreshAccountsButton;

    @Override
    public void initialize(URL location, ResourceBundle resources) {

        CommonYouPinHttpUrl youPinHttpClient = new CommonYouPinHttpUrl();
        // 初始化服务
        transactionAssistantService = new TransactionAssistantServiceImpl();
        accountManagerService = new AccountManagerServiceImpl();
        youPinService = new YouPinServiceImpl();

        // 初始化界面
        showInventoryPane();

        // 设置按钮点击事件
        inventoryButton.setOnAction(event -> showInventoryPane());
        onSaleButton.setOnAction(event -> showOnSalePane());
        confirmButton.setOnAction(event -> showConfirmPane());
        priceButton.setOnAction(event -> showPricePane());

        // 设置焦点样式和键盘导航
        setupTabFocusNavigation();

        // 初始化排序选择器
        initSortSelector();

        // 初始化在售面板平台选择器
        initOnSellPlateSelector();

        // 设置刷新按钮事件
        if (refreshButton != null) {
            refreshButton.setOnAction(event -> refreshInventory());
        }

        // 设置合并/取消合并按钮事件
        if (mergeItemsButton != null) {
            mergeItemsButton.setOnAction(event -> {
                currentSubType = 1; // 合并饰品
                refreshInventory();
            });
        }

        if (unmergeItemsButton != null) {
            unmergeItemsButton.setOnAction(event -> {
                currentSubType = 0; // 拆分（默认情况）
                refreshInventory();
            });
        }

        // 设置上架市场按钮事件
        if (uploadToMarketButton != null) {
            uploadToMarketButton.setOnAction(event -> openSelectedItemsListingDialog());
            // 初始状态禁用上架按钮
            uploadToMarketButton.setDisable(true);
        }

        // 设置全选复选框事件
        if (selectAllCheckbox != null) {
            selectAllCheckbox.setOnAction(event -> handleSelectAllCheckbox());
        }

        // 设置在售全选复选框事件
        if (onSaleSelectAllCheckbox != null) {
            onSaleSelectAllCheckbox.setOnAction(event -> handleOnSaleSelectAllCheckbox());
        }

        // 设置下架按钮事件
        if (unlistButton != null) {
            unlistButton.setOnAction(event -> handleBatchUnlisted());
            // 初始状态禁用下架按钮
            unlistButton.setDisable(true);
        }

        // 设置修改价格按钮事件
        if (modifyPriceButton != null) {
            modifyPriceButton.setOnAction(event -> handleBatchPriceModify());
            // 初始状态禁用修改价格按钮
            modifyPriceButton.setDisable(true);
        }

        // 初始化分页控件
        initPagination();

        // 设置刷新账号按钮事件
        if (refreshAccountsButton != null) {
            refreshAccountsButton.setOnAction(event -> loadSteamAccounts());
        }

        // 加载绑定的steam账号
        loadSteamAccounts();

        // 设置库存搜索框事件处理
        if (searchField != null) {
            // 添加回车键事件处理
            searchField.setOnAction(event -> handleSearchEnter());
        }

        // 设置在售搜索框事件处理
        if (onSaleSearchField != null) {
            // 添加回车键事件处理
            onSaleSearchField.setOnAction(event -> handleOnSaleSearchEnter());
        }
    }

    /**
     * 设置标签页焦点导航
     */
    private void setupTabFocusNavigation() {
        // 创建标签按钮列表，用于键盘导航
        List<Button> tabButtons = Arrays.asList(inventoryButton, onSaleButton, confirmButton, priceButton);

        // 为每个标签按钮添加键盘事件处理
        for (Button button : tabButtons) {
            // 设置按钮可聚焦
            button.setFocusTraversable(true);

            // 添加键盘事件处理
            button.setOnKeyPressed(event -> {
                switch (event.getCode()) {
                    case LEFT:
                        // 左箭头键 - 移动到前一个标签
                        int prevIndex = tabButtons.indexOf(button) - 1;
                        if (prevIndex < 0) prevIndex = tabButtons.size() - 1;
                        tabButtons.get(prevIndex).requestFocus();
                        break;
                    case RIGHT:
                        // 右箭头键 - 移动到下一个标签
                        int nextIndex = (tabButtons.indexOf(button) + 1) % tabButtons.size();
                        tabButtons.get(nextIndex).requestFocus();
                        break;
                    case ENTER:
                    case SPACE:
                        // 回车键或空格键 - 激活当前标签
                        button.fire();
                        break;
                    default:
                        break;
                }
            });

            // 添加焦点事件处理
            button.focusedProperty().addListener((observable, oldValue, newValue) -> {
                if (newValue) {
                    // 获得焦点时添加焦点样式
                    button.getStyleClass().add("focused-tab");
                } else {
                    // 失去焦点时移除焦点样式
                    button.getStyleClass().remove("focused-tab");
                }
            });
        }

        // 默认将焦点设置在当前活动的标签上
        inventoryButton.requestFocus();
    }

    /**
     * 初始化排序选择器
     */
    private void initSortSelector() {
        // 初始化库存排序选择器
        if (inventorySortSelector != null) {
            // 默认选择第一项
            inventorySortSelector.getSelectionModel().select(0);

            // 添加事件处理
            inventorySortSelector.setOnAction(event -> {
                int selectedIndex = inventorySortSelector.getSelectionModel().getSelectedIndex();
                switch (selectedIndex) {
                    case 1:
                        currentSort = 11; // 价格升序
                        break;
                    case 2:
                        currentSort = 12; // 价格降序
                        break;
                    case 3:
                        currentSort = 21; // 冷却时间升序
                        break;
                    case 4:
                        currentSort = 22; // 冷却时间降序
                        break;
                    case 5:
                        currentSort = 31; // 磨损升序
                        break;
                    case 6:
                        currentSort = 32; // 磨损降序
                        break;
                    default:
                        currentSort = 0; // 默认排序
                }
                // 刷新库存（非在售物品）
                currentOnSell = false;
                currentType = 0;  // 库存物品使用默认type值0
                refreshInventory();
            });
        }

        // 初始化在售排序选择器
        if (onSaleSortSelector != null) {
            // 默认选择第一项
            onSaleSortSelector.getSelectionModel().select(0);

            // 添加事件处理
            onSaleSortSelector.setOnAction(event -> {
                int selectedIndex = onSaleSortSelector.getSelectionModel().getSelectedIndex();
                switch (selectedIndex) {
                    case 1:
                        currentSort = 11; // 价格升序
                        break;
                    case 2:
                        currentSort = 12; // 价格降序
                        break;
                    case 3:
                        currentSort = 21; // 冷却时间升序
                        break;
                    case 4:
                        currentSort = 22; // 冷却时间降序
                        break;
                    case 5:
                        currentSort = 31; // 磨损升序
                        break;
                    case 6:
                        currentSort = 32; // 磨损降序
                        break;
                    default:
                        currentSort = 0; // 默认排序
                }
                // 刷新在售物品
                currentOnSell = true;
                currentType = 2;  // 在售物品需要设置type为2
                refreshInventory();
            });
        }
    }

    /**
     * 初始化分页控件
     */
    private void initPagination() {
        if (inventoryPagination != null) {
            inventoryPagination.setPageCount(1);
            inventoryPagination.setCurrentPageIndex(0);
            inventoryPagination.setPageFactory(this::createInventoryPage);
        }

        if (onSalePagination != null) {
            onSalePagination.setPageCount(1);
            onSalePagination.setCurrentPageIndex(0);
            onSalePagination.setPageFactory(this::createOnSalePage);
        }
    }

    /**
     * 显示库存面板
     */
    private void showInventoryPane() {
        // 更新按钮样式
        inventoryButton.getStyleClass().add("active-tab");
        onSaleButton.getStyleClass().removeAll("active-tab");
        confirmButton.getStyleClass().removeAll("active-tab");
        priceButton.getStyleClass().removeAll("active-tab");

        // 设置当前查询参数
        currentOnSell = false;
        currentType = 0;  // 库存物品使用默认type值0
        currentSubType = 0;  // 默认使用拆分模式
        currentSearch = "";  // 清空搜索词

        // 清空搜索框
        if (searchField != null) {
            searchField.clear();
        }

        // 显示库存面板
        inventoryPane.setVisible(true);
        onSalePane.setVisible(false);
        confirmPane.setVisible(false);
        pricePane.setVisible(false);

        // 刷新库存数据
        refreshInventory();
    }

    /**
     * 在售面板平台选择
     */
    private void initOnSellPlateSelector() {
        onSellPlateSelector.getSelectionModel().select(0);
        onSellPlateSelector.setOnAction(event -> {
            int selectedIndex = onSellPlateSelector.getSelectionModel().getSelectedIndex();
            switch (selectedIndex){
                // 全部
                case 0:
                    currentOnSell = true;
                    currentType = 999; // 使用特殊值标识显示所有平台数据
                    currentSearch = "";
                    refreshInventory();
                    break;
                // IO661
                case 1:
                    currentOnSell = true;
                    currentType = 2;
                    currentSearch = "";
                    refreshInventory();
                    break;
                // 悠悠有品
                case 2:
                    currentOnSell = true;
                    currentType = -1; // 使用特殊值标识悠悠有品数据源
                    currentSearch = "";
                    refreshInventory();
                    break;
                // 网易BUFF
                case 3:
                    currentOnSell = true;
                    currentType = 5;
                    currentSearch = "";
                    refreshInventory();
                    break;
                // 默认全部
                default:
                    currentOnSell = true;
                    currentType = 999;
                    currentSearch = "";
                    refreshInventory();
                    break;
            }
        });

    }

    /**
     * 显示在售面板
     */
    private void showOnSalePane() {
        // 更新按钮样式
        inventoryButton.getStyleClass().removeAll("active-tab");
        onSaleButton.getStyleClass().add("active-tab");
        confirmButton.getStyleClass().removeAll("active-tab");
        priceButton.getStyleClass().removeAll("active-tab");

        // 设置当前查询参数
        currentOnSell = true;
        currentType = 999;  // 使用特殊值999显示所有平台数据
        currentSearch = "";  // 清空搜索词

        // 重置平台选择器到"全部"
        if (onSellPlateSelector != null) {
            onSellPlateSelector.getSelectionModel().select(0);
        }

        // 清空搜索框
        if (onSaleSearchField != null) {
            onSaleSearchField.clear();
        }

        // 显示在售面板
        inventoryPane.setVisible(false);
        onSalePane.setVisible(true);
        confirmPane.setVisible(false);
        pricePane.setVisible(false);

        // 刷新在售数据
        refreshInventory();
    }

    /**
     * 显示确认页面
     */
    private void showConfirmPane() {
        // 更新按钮样式
        inventoryButton.getStyleClass().removeAll("active-tab");
        onSaleButton.getStyleClass().removeAll("active-tab");
        confirmButton.getStyleClass().add("active-tab");
        priceButton.getStyleClass().removeAll("active-tab");

        // 显示确认面板
        inventoryPane.setVisible(false);
        onSalePane.setVisible(false);
        confirmPane.setVisible(true);
        pricePane.setVisible(false);
    }
    /**
     * 显示报价页面
     */
    private void showPricePane() {
        // 更新按钮样式
        inventoryButton.getStyleClass().removeAll("active-tab");
        onSaleButton.getStyleClass().removeAll("active-tab");
        confirmButton.getStyleClass().removeAll("active-tab");
        priceButton.getStyleClass().add("active-tab");

        // 显示报价面板
        inventoryPane.setVisible(false);
        onSalePane.setVisible(false);
        confirmPane.setVisible(false);
        pricePane.setVisible(true);
    }

    /**
     * 加载Steam账号数据
     */
    private void loadSteamAccounts() {
        String token = LoginController.getAuthToken();
        if (token == null || token.isEmpty()) {
            System.out.println("未找到授权令牌，无法加载Steam账号");
            return;
        }

        // 在后台线程中加载数据
        new Thread(() -> {
            List<SteamRes.SteamBind> steamBindList = accountManagerService.getAllSteamAccount(token);

            // 在JavaFX线程中更新UI
            Platform.runLater(() -> {
                if (steamBindList != null && !steamBindList.isEmpty()) {
                    steamAccounts.clear();
                    steamAccounts.addAll(steamBindList);
                    System.out.println("成功加载 " + steamBindList.size() + " 个Steam账号");

                    // 更新平台选择器和账号选择器
                    updatePlatformSelector();
                    updateAccountSelector();

                    // 默认选择第一个账号
                    if (!steamAccounts.isEmpty()) {
                        selectedSteamAccount = steamAccounts.getFirst();
                        updateSteamAccountInfo(selectedSteamAccount);

                        // 加载该账号的库存
                        currentSteamId = selectedSteamAccount.getSteamId();
                        loadInventory();
                    }
                } else {
                    System.out.println("未找到Steam账号或加载失败");
                }
            });
        }).start();
    }

    /**
     * 更新平台选择器（用于筛选功能）
     */
    private void updatePlatformSelector() {
        if (platformSelector != null) {
            platformSelector.getItems().clear();

            // 添加筛选选项
            MenuItem allItem = new MenuItem("全部");
            allItem.setOnAction(event -> {
                platformSelector.setText("全部");
                currentSteamId = "";
                // 保持当前页面的type和onSell设置
                // 如果当前是在售页面，确保type=2和onSell=true
                if (currentOnSell) {
                    currentType = 2;
                } else {
                    currentType = 0;
                    currentSubType = 0;  // 默认使用拆分模式
                }
                refreshInventory();
            });
            platformSelector.getItems().add(allItem);

            // 默认选择"全部"
            platformSelector.setText("全部");
        }
    }

    /**
     * 更新账号选择器
     */
    private void updateAccountSelector() {
        if (accountListContainer != null) {
            // 清空现有账号列表
            accountListContainer.getChildren().clear();

            // 添加"全部"选项
            try {
                HBox allAccountCard = createAllAccountCard();
                allAccountCard.setOnMouseClicked(event -> {
                    // 更新选中状态
                    updateSelectedAccount(null);
                    // 更新显示标签
                    accountSelectorLabel.setText("全部");
                    // 清空当前选中的账号ID
                    currentSteamId = "";
                    // 保持当前页面的type和onSell设置
                    // 如果当前是在售页面，确保type=2和onSell=true
                    if (currentOnSell) {
                        currentType = 2;
                    } else {
                        currentType = 0;
                        currentSubType = 0;  // 默认使用拆分模式
                    }
                    // 刷新库存
                    refreshInventory();
                });
                accountListContainer.getChildren().add(allAccountCard);

                // 默认选中"全部"
                updateSelectedAccount(null);
                accountSelectorLabel.setText("全部");
            } catch (Exception e) {
                System.out.println("创建全部账号卡片异常: " + e.getMessage());
            }

            // 添加每个Steam账号
            for (SteamRes.SteamBind steamAccount : steamAccounts) {
                try {
                    // 加载小型账号卡片
                    FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/account-mini-card.fxml"));
                    HBox accountCard = loader.load();

                    // 配置账号卡片数据
                    configureMiniAccountCard(accountCard, steamAccount);

                    // 添加点击事件
                    accountCard.setOnMouseClicked(event -> {
                        // 更新选中状态
                        updateSelectedAccount(accountCard);
                        // 更新显示标签
                        accountSelectorLabel.setText(steamAccount.getNickname());
                        // 更新选中的账号
                        selectedSteamAccount = steamAccount;
                        updateSteamAccountInfo(selectedSteamAccount);
                        // 更新当前选中的账号ID
                        currentSteamId = steamAccount.getSteamId();
                        // 保持当前页面的type和onSell设置
                        // 如果当前是在售页面，确保type=2和onSell=true
                        if (currentOnSell) {
                            currentType = 2;
                        } else {
                            currentType = 0;
                            currentSubType = 0;  // 默认使用拆分模式
                        }
                        // 刷新库存
                        refreshInventory();
                    });

                    // 添加到容器
                    accountListContainer.getChildren().add(accountCard);
                } catch (Exception e) {
                    System.out.println("创建账号卡片异常: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 创建"全部"账号卡片
     */
    private HBox createAllAccountCard() {
        HBox allAccountCard = new HBox();
        allAccountCard.getStyleClass().add("account-mini-card");
        allAccountCard.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        allAccountCard.setSpacing(8);
        allAccountCard.setPadding(new Insets(5, 8, 5, 8));

        // 添加图标
        StackPane iconContainer = new StackPane();
        iconContainer.getStyleClass().add("avatar-mini-container");

        Circle circle = new Circle(15);
        circle.getStyleClass().add("avatar-circle");

        // 为"全部账号"使用 Logo.png 作为头像图标
        ImageView imageView = new ImageView(new Image(Objects.requireNonNull(getClass().getResourceAsStream(DEFAULT_IMAGE_URL))));
        imageView.setFitHeight(30);
        imageView.setFitWidth(30);
        imageView.setPreserveRatio(true);

        iconContainer.getChildren().addAll(circle, imageView);

        // 添加文本
        Label label = new Label("全部账号");
        label.getStyleClass().add("username-mini-label");

        // 添加到卡片
        allAccountCard.getChildren().addAll(iconContainer, label);

        return allAccountCard;
    }

    /**
     * 配置小型账号卡片
     */
    private void configureMiniAccountCard(HBox accountCard, SteamRes.SteamBind steamAccount) {
        try {
            // 获取卡片中的UI元素
            Label usernameLabel = (Label) accountCard.lookup("#usernameLabel");
            Label balanceLabel = (Label) accountCard.lookup("#balanceLabel");
            ImageView avatarImage = (ImageView) accountCard.lookup("#avatarImage");
            Circle statusCircle = (Circle) accountCard.lookup("#statusCircle");

            // 设置昵称
            if (usernameLabel != null) {
                usernameLabel.setText(steamAccount.getNickname());
            }

            // 设置余额（如果有）
            if (balanceLabel != null) {
                balanceLabel.setText("ID: " + steamAccount.getSteamId().substring(0, 8) + "...");
            }

            // 设置状态
            if (statusCircle != null) {
                if (steamAccount.isEffective()) {
                    statusCircle.getStyleClass().removeAll("warning", "error");
                    statusCircle.setFill(javafx.scene.paint.Color.valueOf("#4caf50"));
                } else {
                    statusCircle.getStyleClass().add("error");
                    statusCircle.setFill(javafx.scene.paint.Color.valueOf("#f44336"));
                }
            }

            // 设置头像 - 使用接口返回的 avatar
            if (avatarImage != null) {
                String avatarUrl = steamAccount.getAvatar();
                if (avatarUrl != null && !avatarUrl.isEmpty()) {
                    try {
                        Image avatar = new Image(avatarUrl, true);
                        avatarImage.setImage(avatar);
                    } catch (Exception e) {
                        System.out.println("加载头像失败: " + e.getMessage());
                        // 使用默认头像
                        avatarImage.setImage(new Image(Objects.requireNonNull(getClass().getResourceAsStream("/com/io661/extension/img/avatar.png"))));
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("配置小型账号卡片异常: " + e.getMessage());
        }
    }

    /**
     * 更新选中的账号卡片样式
     */
    private void updateSelectedAccount(Node selectedCard) {
        // 移除所有卡片的选中样式
        for (Node card : accountListContainer.getChildren()) {
            card.getStyleClass().remove("selected");
        }

        // 为选中的卡片添加选中样式
        if (selectedCard != null) {
            selectedCard.getStyleClass().add("selected");
        } else {
            // 如果是null，则选中"全部"卡片
            if (!accountListContainer.getChildren().isEmpty()) {
                accountListContainer.getChildren().getFirst().getStyleClass().add("selected");
            }
        }
    }

    /**
     * 更新Steam账号信息显示
     */
    private void updateSteamAccountInfo(SteamRes.SteamBind steamAccount) {
        if (steamAccount != null) {
            Platform.runLater(() -> {
                // 更新用户名
                if (usernameLabel != null) {
                    usernameLabel.setText(steamAccount.getNickname());
                }

                // 更新交易URL
                if (tradeUrlLabel != null) {
                    tradeUrlLabel.setText(steamAccount.getTradeUrl());
                }

                // 更新Steam ID
                if (steamIdLabel != null) {
                    steamIdLabel.setText(steamAccount.getSteamId());
                }

                // 更新头像 - 使用接口返回的 avatar
                if (avatarImageView != null && steamAccount.getAvatar() != null && !steamAccount.getAvatar().isEmpty()) {
                    try {
                        Image avatarImage = new Image(steamAccount.getAvatar());
                        avatarImageView.setImage(avatarImage);
                    } catch (Exception e) {
                        System.out.println("加载头像图片失败: " + e.getMessage());
                        // 使用默认头像
                        avatarImageView.setImage(new Image(Objects.requireNonNull(getClass().getResourceAsStream("/com/io661/extension/img/avatar.png"))));
                    }
                }
            });
        }
    }

    /**
     * 加载库存数据
     */
    private void loadInventory() {
        // 显示加载中提示
        showLoading();

        // 在后台线程中加载数据
        new Thread(() -> {
            try {
                List<SteamInventoryRes> items;

                // 检查数据源类型
                if (currentType == 999) {
                    // 全部平台：合并IO661和悠悠有品数据
                    items = new ArrayList<>();

                    // 加载IO661数据
                    try {
                        List<SteamInventoryRes> io661Items = transactionAssistantService.getInventoryList(
                                2, // IO661类型
                                currentSteamId,
                                currentLimit,
                                currentSubType,
                                currentOnSell,
                                currentSort,
                                currentSearch
                        );
                        if (io661Items != null) {
                            // 为IO661数据添加平台标识
                            for (SteamInventoryRes item : io661Items) {
                                item.setPlatformType("io661");
                            }
                            items.addAll(io661Items);
                        }
                    } catch (Exception e) {
                        System.err.println("加载IO661数据失败: " + e.getMessage());
                    }

                    // 加载悠悠有品数据
                    try {
                        List<SteamInventoryRes> youPinItems;
                        if (currentSteamId != null && !currentSteamId.isEmpty()) {
                            youPinItems = loadYouPinOnSellData(currentSteamId);
                        } else {
                            youPinItems = new ArrayList<>();
                            for (SteamRes.SteamBind account : steamAccounts) {
                                List<SteamInventoryRes> accountItems = loadYouPinOnSellData(account.getSteamId());
                                youPinItems.addAll(accountItems);
                            }
                        }
                        if (youPinItems != null) {
                            // 为悠悠有品数据添加平台标识
                            for (SteamInventoryRes item : youPinItems) {
                                item.setPlatformType("youpin");
                            }
                            items.addAll(youPinItems);
                        }
                    } catch (Exception e) {
                        System.err.println("加载悠悠有品数据失败: " + e.getMessage());
                    }

                } else if (currentType == -1) {
                    // 悠悠有品数据源
                    if (currentSteamId != null && !currentSteamId.isEmpty()) {
                        items = loadYouPinOnSellData(currentSteamId);
                    } else {
                        items = new ArrayList<>();
                        for (SteamRes.SteamBind account : steamAccounts) {
                            List<SteamInventoryRes> accountItems = loadYouPinOnSellData(account.getSteamId());
                            items.addAll(accountItems);
                        }
                    }
                    // 为悠悠有品数据添加平台标识
                    if (items != null) {
                        for (SteamInventoryRes item : items) {
                            item.setPlatformType("youpin");
                        }
                    }
                } else {
                    // IO661或其他平台数据
                    items = transactionAssistantService.getInventoryList(
                            currentType,
                            currentSteamId,
                            currentLimit,
                            currentSubType,
                            currentOnSell,
                            currentSort,
                            currentSearch
                    );
                    // 为IO661数据添加平台标识
                    if (items != null) {
                        for (SteamInventoryRes item : items) {
                            item.setPlatformType("io661");

                            // 如果是库存页面且物品在售，需要检查其他平台的在售状态
                            if (!currentOnSell && item.isOnSell()) {
                                // 检查该物品在其他平台的在售状态
                                checkMultiPlatformOnSellStatus(item);
                            }
                        }
                    }
                }

                // 在JavaFX线程中更新UI
                Platform.runLater(() -> {
                    hideLoading();
                    if (items != null) {
                        // 清空库存和选中的物品
                        inventoryItems.clear();
                        selectedItems.clear();

                        // 添加新的库存物品
                        inventoryItems.addAll(items);
                        String itemType = currentOnSell ? "在售物品" : "库存物品";
                        System.out.println("成功加载 " + items.size() + " 个" + itemType);

                        // 更新全选复选框和上架按钮状态
                        updateSelectAllCheckbox();
                        updateUploadButtonState();

                        // 更新库存统计信息
                        updateInventorySummary();

                        // 更新分页控件
                        updatePagination();

                        // 渲染物品
                        renderInventoryItems();
                    } else {
                        String itemType = currentOnSell ? "在售物品" : "库存物品";
                        System.out.println("未找到" + itemType + "或加载失败");
                        inventoryItems.clear();
                        if (currentOnSell) {
                            if (onSaleItemsContainer != null) {
                                onSaleItemsContainer.getChildren().clear();
                                Label emptyLabel = new Label("没有找到在售物品");
                                emptyLabel.getStyleClass().add("empty-message");
                                onSaleItemsContainer.getChildren().add(emptyLabel);
                            }
                        } else {
                            if (inventoryItemsContainer != null) {
                                inventoryItemsContainer.getChildren().clear();
                                Label emptyLabel = new Label("没有找到库存物品");
                                emptyLabel.getStyleClass().add("empty-message");
                                inventoryItemsContainer.getChildren().add(emptyLabel);
                            }
                        }
                    }
                });
            } catch (Exception e) {
                System.out.println("加载数据异常: " + e.getMessage());
                Platform.runLater(() -> {
                    hideLoading();
                    inventoryItems.clear();
                    if (currentOnSell) {
                        if (onSaleItemsContainer != null) {
                            onSaleItemsContainer.getChildren().clear();
                            Label emptyLabel = new Label("加载在售物品失败");
                            emptyLabel.getStyleClass().add("empty-message");
                            onSaleItemsContainer.getChildren().add(emptyLabel);
                        }
                    } else {
                        if (inventoryItemsContainer != null) {
                            inventoryItemsContainer.getChildren().clear();
                            Label emptyLabel = new Label("加载库存物品失败");
                            emptyLabel.getStyleClass().add("empty-message");
                            inventoryItemsContainer.getChildren().add(emptyLabel);
                        }
                    }
                });
            }
        }).start();
    }

    /**
     * 刷新库存
     */
    private void refreshInventory() {
        loadInventory();
    }

    /**
     * 显示加载中提示
     */
    private void showLoading() {
        Platform.runLater(() -> {
            if (loadingPane != null) {
                loadingPane.setVisible(true);
            }
        });
    }

    /**
     * 隐藏加载中提示
     */
    private void hideLoading() {
        Platform.runLater(() -> {
            if (loadingPane != null) {
                loadingPane.setVisible(false);
            }
        });
    }

    /**
     * 更新库存统计信息
     */
    private void updateInventorySummary() {
        if (inventorySummaryLabel != null) {
            int itemCount = inventoryItems.size();

            // 计算总价值（单位：分）
            int totalValueInCents = 0;
            for (SteamInventoryRes item : inventoryItems) {
                if (item.getRefPrice() != null) {
                    totalValueInCents += item.getRefPrice();
                }
            }

            // 转换为元
            double totalValueInYuan = totalValueInCents / 100.0;

            // 格式化日期时间
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm");
            String formattedDateTime = now.format(formatter);

            // 更新标签文本
            DecimalFormat df = new DecimalFormat("0.00");
            inventorySummaryLabel.setText("共 " + itemCount + " 件物品 总值 ¥ " + df.format(totalValueInYuan) + " 上次刷新时间: " + formattedDateTime);
        }
    }

    /**
     * 更新分页控件
     */
    private void updatePagination() {
        // 计算总页数 (每页显示20个物品)
        int totalPages = (int) Math.ceil(inventoryItems.size() / 20.0);
        if (totalPages == 0) totalPages = 1;

        if (currentOnSell) {
            // 更新在售分页控件
            if (onSalePagination != null) {
                onSalePagination.setPageCount(totalPages);
                onSalePagination.setCurrentPageIndex(0);
            }
        } else {
            // 更新库存分页控件
            if (inventoryPagination != null) {
                inventoryPagination.setPageCount(totalPages);
                inventoryPagination.setCurrentPageIndex(0);
            }
        }
    }

    /**
     * 创建库存页面
     */
    private FlowPane createInventoryPage(int pageIndex) {
        FlowPane page = new FlowPane();
        page.setHgap(10);
        page.setVgap(10);
        page.setPadding(new Insets(10));

        int startIndex = pageIndex * 20;
        int endIndex = Math.min(startIndex + 20, inventoryItems.size());

        for (int i = startIndex; i < endIndex; i++) {
            SteamInventoryRes item = inventoryItems.get(i);
            VBox itemCard = createItemCard(item);
            page.getChildren().add(itemCard);
        }

        return page;
    }

    /**
     * 创建在售页面
     */
    private FlowPane createOnSalePage(int pageIndex) {
        FlowPane page = new FlowPane();
        page.setHgap(10);
        page.setVgap(10);
        page.setPadding(new Insets(10));

        // 确保当前是在售模式
        if (currentOnSell && !inventoryItems.isEmpty()) {
            int startIndex = pageIndex * 20;
            int endIndex = Math.min(startIndex + 20, inventoryItems.size());

            for (int i = startIndex; i < endIndex; i++) {
                SteamInventoryRes item = inventoryItems.get(i);
                VBox itemCard = createItemCard(item);
                page.getChildren().add(itemCard);
            }
        } else if (inventoryItems.isEmpty()) {
            Label emptyLabel = new Label("没有找到在售物品");
            emptyLabel.getStyleClass().add("empty-message");
            page.getChildren().add(emptyLabel);
        }

        return page;
    }

    /**
     * 渲染物品
     */
    private void renderInventoryItems() {
        if (currentOnSell) {
            // 渲染在售物品
            if (onSaleItemsContainer != null) {
                onSaleItemsContainer.getChildren().clear();

                // 直接将物品添加到容器中
                for (SteamInventoryRes item : inventoryItems) {
                    VBox itemCard = createItemCard(item);
                    onSaleItemsContainer.getChildren().add(itemCard);
                }

                // 如果没有物品，显示提示信息
                if (inventoryItems.isEmpty()) {
                    Label emptyLabel = new Label("没有找到在售物品");
                    emptyLabel.getStyleClass().add("empty-message");
                    onSaleItemsContainer.getChildren().add(emptyLabel);
                }
            }
        } else {
            // 渲染库存物品
            if (inventoryItemsContainer != null) {
                inventoryItemsContainer.getChildren().clear();

                // 直接将物品添加到容器中
                for (SteamInventoryRes item : inventoryItems) {
                    VBox itemCard = createItemCard(item);
                    inventoryItemsContainer.getChildren().add(itemCard);
                }

                // 如果没有物品，显示提示信息
                if (inventoryItems.isEmpty()) {
                    Label emptyLabel = new Label("没有找到库存物品");
                    emptyLabel.getStyleClass().add("empty-message");
                    inventoryItemsContainer.getChildren().add(emptyLabel);
                }
            }
        }
    }

    /**
     * 获取磨损等级名称
     * @param floatValue 磨损值
     * @return 磨损等级名称
     */
    private String getWearCategoryName(BigDecimal floatValue) {
        if (floatValue == null) {
            return "";
        }

        double value = floatValue.doubleValue();

        if (value >= 0 && value < 0.07) {
            return "崭新出厂";
        } else if (value >= 0.07 && value < 0.15) {
            return "略有磨损";
        } else if (value >= 0.15 && value < 0.38) {
            return "久经沙场";
        } else if (value >= 0.38 && value < 0.45) {
            return "破损不堪";
        } else if (value >= 0.45 && value <= 1.0) {
            return "战痕累累";
        } else {
            return "";
        }
    }

    /**
     * 检查字符串是否为磨损等级名称
     * @param text 要检查的文本
     * @return 是否为磨损等级名称
     */
    private boolean isWearCategory(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }

        // 检查是否为已知的磨损等级名称
        return text.equals("崭新出厂") ||
                text.equals("略有磨损") ||
                text.equals("久经沙场") ||
                text.equals("破损不堪") ||
                text.equals("战痕累累");
    }

    /**
     * 创建物品卡片
     */
    private VBox createItemCard(SteamInventoryRes item) {
        VBox itemCard = new VBox();
        itemCard.getStyleClass().add("item-card");
        itemCard.setPadding(new Insets(5));
        itemCard.setSpacing(2);
        itemCard.setPrefWidth(180);
        itemCard.setPrefHeight(180);

        // 添加顶部容器
        HBox topContainer = new HBox();
        topContainer.setSpacing(5);
        topContainer.setPadding(new Insets(2, 0, 2, 0));

        // 添加标签容器（StatTrak™/可交易等）
        // 根据物品状态设置标签
        if (item.getHashName() != null && item.getHashName().contains("StatTrak")) {
            Label statTrakLabel = new Label("StatTrak™");
            statTrakLabel.getStyleClass().addAll("tag-label", "stattrak-tag");
            topContainer.getChildren().add(statTrakLabel);
        }

        // 添加可交易标签
        if (item.getTradable()) {
            Label tradableLabel = new Label("可交易");
            tradableLabel.getStyleClass().addAll("tag-label", "tradable-tag");
            topContainer.getChildren().add(tradableLabel);
        } else {
            Label lockedLabel = new Label("不可交易");
            lockedLabel.getStyleClass().addAll("tag-label", "locked-tag");
            topContainer.getChildren().add(lockedLabel);
        }

        // 在售标签和平台标识
        if (item.isOnSell()) {
            Label onSaleLabel = new Label("在售中");
            onSaleLabel.getStyleClass().addAll("tag-label", "on-sale-tag");
            topContainer.getChildren().add(onSaleLabel);

            // 添加平台标识（显示所有在售平台的logo）
            List<String> onSellPlatforms = item.getOnSellPlatforms();
            if (onSellPlatforms != null && !onSellPlatforms.isEmpty()) {
                // 显示多个平台的logo
                for (String platformType : onSellPlatforms) {
                    ImageView platformIcon = new ImageView();
                    platformIcon.setFitHeight(12);
                    platformIcon.setFitWidth(12);
                    platformIcon.setPreserveRatio(true);

                    try {
                        if ("youpin".equals(platformType)) {
                            platformIcon.setImage(new Image(Objects.requireNonNull(getClass().getResourceAsStream("/com/io661/extension/img/youpin.png"))));
                            Tooltip tooltip = new Tooltip("悠悠有品");
                            Tooltip.install(platformIcon, tooltip);
                        } else if ("io661".equals(platformType)) {
                            platformIcon.setImage(new Image(Objects.requireNonNull(getClass().getResourceAsStream("/com/io661/extension/img/io661.png"))));
                            Tooltip tooltip = new Tooltip("IO661");
                            Tooltip.install(platformIcon, tooltip);
                        } else if ("buff".equals(platformType)) {
                            // 如果有BUFF图标的话
                            platformIcon.setImage(new Image(Objects.requireNonNull(getClass().getResourceAsStream("/com/io661/extension/img/buff.png"))));
                            Tooltip tooltip = new Tooltip("网易BUFF");
                            Tooltip.install(platformIcon, tooltip);
                        }

                        if (platformIcon.getImage() != null) {
                            topContainer.getChildren().add(platformIcon);
                        }
                    } catch (Exception e) {
                        System.out.println("加载平台图标失败: " + e.getMessage());
                    }
                }
            } else if (item.getPlatformType() != null) {
                // 兼容旧的单平台标识方式
                ImageView platformIcon = new ImageView();
                platformIcon.setFitHeight(12);
                platformIcon.setFitWidth(12);
                platformIcon.setPreserveRatio(true);

                try {
                    String platformType = item.getPlatformType();
                    if ("youpin".equals(platformType)) {
                        platformIcon.setImage(new Image(Objects.requireNonNull(getClass().getResourceAsStream("/com/io661/extension/img/youpin.png"))));
                        Tooltip tooltip = new Tooltip("悠悠有品");
                        Tooltip.install(platformIcon, tooltip);
                    } else if ("io661".equals(platformType)) {
                        platformIcon.setImage(new Image(Objects.requireNonNull(getClass().getResourceAsStream("/com/io661/extension/img/io661.png"))));
                        Tooltip tooltip = new Tooltip("IO661");
                        Tooltip.install(platformIcon, tooltip);
                    } else if ("buff".equals(platformType)) {
                        // 如果有BUFF图标的话
                        platformIcon.setImage(new Image(Objects.requireNonNull(getClass().getResourceAsStream("/com/io661/extension/img/buff.png"))));
                        Tooltip tooltip = new Tooltip("网易BUFF");
                        Tooltip.install(platformIcon, tooltip);
                    }

                    if (platformIcon.getImage() != null) {
                        topContainer.getChildren().add(platformIcon);
                    }
                } catch (Exception e) {
                    System.out.println("加载平台图标失败: " + e.getMessage());
                }
            }
        }

        // 添加数量标签（如果是合并模式且数量大于1）
        if (item.getQuantity() != null && item.getQuantity() > 1) {
            Label quantityLabel = new Label("x" + item.getQuantity());
            quantityLabel.getStyleClass().addAll("tag-label", "quantity-tag");
            topContainer.getChildren().add(quantityLabel);
        }

        // 移除顶部的磨损值显示，因为我们已经在底部显示了磨损值和磨损文字

        // 添加物品图片容器
        StackPane imageContainer = new StackPane();
        imageContainer.getStyleClass().add("item-image-container");
        imageContainer.setPrefHeight(100);
        imageContainer.setPrefWidth(170);
        imageContainer.setAlignment(javafx.geometry.Pos.CENTER);

        ImageView itemImage = new ImageView();
        itemImage.setFitHeight(90);
        itemImage.setFitWidth(130);
        itemImage.setPreserveRatio(true);

        // 设置图片URL
        if (item.getIconUrl() != null && !item.getIconUrl().isEmpty()) {
            try {
                // 使用 backgroundLoading=true 防止阻塞UI线程
                Image image = new Image(item.getIconUrl(), true);

                // 添加图片加载错误处理
                image.errorProperty().addListener((observable, oldValue, newValue) -> {
                    if (newValue) {
                        System.out.println("图片加载失败: " + item.getIconUrl());
                        // 图片加载失败时不显示任何图片
                        itemImage.setImage(null);
                    }
                });

                itemImage.setImage(image);
            } catch (Exception e) {
                System.out.println("图片加载异常: " + e.getMessage());
                // 如果加载失败，不显示任何图片
                itemImage.setImage(null);
            }
        } else {
            // 没有图片URL时不显示任何图片
            itemImage.setImage(null);
        }

        imageContainer.getChildren().add(itemImage);

        // 添加印花（如果有）- 放在武器图片底部水平排列
        if (item.getStickerList() != null && !item.getStickerList().isEmpty()) {
            HBox stickersContainer = new HBox(); // 改为HBox水平排列
            stickersContainer.setSpacing(2);
            stickersContainer.getStyleClass().add("stickers-container");

            // 最多显示5个印花
            int maxStickers = Math.min(item.getStickerList().size(), 5);
            for (int i = 0; i < maxStickers; i++) {
                CommonSticker sticker = item.getStickerList().get(i);

                // 创建印花容器
                StackPane stickerContainer = new StackPane();
                stickerContainer.getStyleClass().add("sticker-container");
                stickerContainer.setPrefSize(18, 18);

                // 创建印花图片
                ImageView stickerImage = new ImageView();
                stickerImage.setFitHeight(16);
                stickerImage.setFitWidth(16);
                stickerImage.setPreserveRatio(true);

                // 设置印花图片URL
                if (sticker.getIconUrl() != null && !sticker.getIconUrl().isEmpty()) {
                    try {
                        Image image = new Image(sticker.getIconUrl(), true);
                        stickerImage.setImage(image);

                        // 添加提示信息（印花名称和磨损）
                        Tooltip tooltip = new Tooltip(sticker.getItemName() + "\n剩余: " + sticker.getWear() + "%");
                        Tooltip.install(stickerImage, tooltip);
                    } catch (Exception ignore) {
                    }
                }

                stickerContainer.getChildren().add(stickerImage);
                stickersContainer.getChildren().add(stickerContainer);
            }

            // 将印花容器添加到图片容器中 - 底部居中
            imageContainer.getChildren().add(stickersContainer);
            StackPane.setAlignment(stickersContainer, javafx.geometry.Pos.BOTTOM_CENTER); // 改为底部居中
            StackPane.setMargin(stickersContainer, new Insets(0, 0, 5, 0)); // 只设置底部边距
        }

        // 创建物品名称和类型标签
        VBox nameContainer = new VBox();
        nameContainer.setSpacing(1);

        // 创建物品名称标签
        Label nameLabel = new Label();
        nameLabel.getStyleClass().add("item-name");
        nameLabel.setWrapText(true);
        nameLabel.setText(item.getItemName() != null ? item.getItemName() : item.getHashName());

        // 创建物品类型标签（如果有）- 但不从名称中提取磨损信息，避免重复
        Label typeLabel = new Label();
        typeLabel.getStyleClass().add("item-type");
        if (item.getHashName() != null) {
            // 提取类型信息，但排除磨损信息
            String type = "";
            if (item.getItemName() != null && item.getItemName().contains("(")) {
                int startIndex = item.getItemName().lastIndexOf("(");
                int endIndex = item.getItemName().lastIndexOf(")");
                if (startIndex > 0 && endIndex > startIndex) {
                    String extractedType = item.getItemName().substring(startIndex + 1, endIndex);
                    // 检查提取的文本是否是磨损等级，如果是则不显示
                    if (!isWearCategory(extractedType)) {
                        type = extractedType;
                    }
                }
            }
            typeLabel.setText(type);
        }

        nameContainer.getChildren().addAll(nameLabel);
        if (!typeLabel.getText().isEmpty()) {
            nameContainer.getChildren().add(typeLabel);
        }

        // 创建底部容器
        VBox bottomContainer = new VBox();
        bottomContainer.setSpacing(2);

        // 如果有磨损值，在价格上方显示磨损值（排除0E-18或null的情况）
        if (item.getFloatValue() != null && item.getFloatValue().compareTo(BigDecimal.ZERO) != 0) {
            // 创建磨损值标签
            Label wearLabel = new Label();
            wearLabel.getStyleClass().add("item-wear");

            // 格式化磨损值显示
            DecimalFormat df = new DecimalFormat("0.00000000");
            String floatText = df.format(item.getFloatValue());

            // 如果磨损值太长，截断显示
            if (floatText.length() > 10) {
                floatText = floatText.substring(0, 10) + "...";
            }

            // 添加磨损分类和磨损值
            String wearCategory = getWearCategoryName(item.getFloatValue());
            if (!wearCategory.isEmpty()) {
                wearLabel.setText(wearCategory + " " + floatText); // 磨损文字后面直接跟磨损值
            } else {
                wearLabel.setText(floatText);
            }

            bottomContainer.getChildren().add(wearLabel);
        }

        // 创建价格和按钮的容器（水平排列）
        HBox priceAndButtonContainer = new HBox();
        priceAndButtonContainer.setAlignment(javafx.geometry.Pos.CENTER);
        priceAndButtonContainer.setSpacing(5);

        // 设置HBox填充整个宽度
        HBox.setHgrow(priceAndButtonContainer, Priority.ALWAYS);

        // 创建价格标签
        Label priceLabel = new Label();
        priceLabel.getStyleClass().add("item-price");

        // 格式化价格显示
        DecimalFormat df = new DecimalFormat("¥ 0.00");

        // 如果物品在售，优先显示在售价格
        if (item.isOnSell() && item.getOnSellPrice() != null && item.getOnSellPrice() > 0) {
            // 在售价格单位为分，转换为元
            double onSellPriceInYuan = item.getOnSellPrice() / 100.0;
            priceLabel.setText(df.format(onSellPriceInYuan));
        }
        // 否则显示参考价格
        else if (item.getRefPrice() != null) {
            // 参考价格单位为分，转换为元
            double refPriceInYuan = item.getRefPrice() / 100.0;
            priceLabel.setText(df.format(refPriceInYuan));
        } else {
            priceLabel.setText("¥ --");
        }

        // 将价格标签添加到左侧
        priceAndButtonContainer.getChildren().add(priceLabel);

        // 添加弹性空间，将价格和按钮分开
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        priceAndButtonContainer.getChildren().add(spacer);

        // 添加按钮（根据物品状态显示不同按钮）
        if (item.getTradable()) {
            if (!item.isOnSell()) {
                // 未在售：显示上架按钮
                Button actionButton = new Button("上架");
                actionButton.getStyleClass().add("item-action-button");
                actionButton.setOnAction(event -> {
                    System.out.println("上架物品: " + item.getItemName());
                    // 打开上架对话框，只上架当前物品
                    openSingleItemListingDialog(item);
                    event.consume(); // 防止事件冒泡到卡片点击事件
                });
                priceAndButtonContainer.getChildren().add(actionButton);
            } else {
                // 在售中：显示修改价格按钮
                Button modifyPriceButton = new Button("改价");
                modifyPriceButton.getStyleClass().add("item-action-button");
                modifyPriceButton.setOnAction(event -> {
                    System.out.println("修改物品价格: " + item.getItemName());
                    // 打开修改价格对话框，只修改当前物品
                    openSingleItemPriceModifyDialog(item);
                    event.consume(); // 防止事件冒泡到卡片点击事件
                });
                priceAndButtonContainer.getChildren().add(modifyPriceButton);
            }
        }

        // 将价格和按钮容器添加到底部容器
        bottomContainer.getChildren().add(priceAndButtonContainer);

        // 将所有组件添加到卡片
        itemCard.getChildren().addAll(topContainer, imageContainer, nameContainer, bottomContainer);

        // 添加点击事件
        itemCard.setOnMouseClicked(event -> handleItemClick(item, itemCard));

        // 如果物品已经被选中，添加选中样式
        if (selectedItems.contains(item)) {
            itemCard.getStyleClass().add("selected-item");
        }

        return itemCard;
    }

    /**
     * 处理物品点击事件
     */
    private void handleItemClick(SteamInventoryRes item, VBox itemCard) {
        // 打印物品信息
        System.out.println("点击了物品: " + (item.getItemName() != null ? item.getItemName() : item.getHashName()));

        // 根据当前页面类型处理物品点击
        if (currentOnSell) {
            // 在售页面：可以选择在售物品进行下架操作
            if (item.getTradable() && item.isOnSell()) {
                toggleItemSelection(item, itemCard);
            }
        } else {
            // 库存页面：只能选择未在售的物品进行上架操作
            if (item.getTradable() && !item.isOnSell()) {
                toggleItemSelection(item, itemCard);
            } else if (item.isOnSell()) {
                System.out.println("库存页面中，在售物品不能被选择: " + (item.getItemName() != null ? item.getItemName() : item.getHashName()));
            }
        }

        // 打印磨损值信息
        if (item.getFloatValue() != null) {
            DecimalFormat df = new DecimalFormat("0.00000000");
            String floatText = df.format(item.getFloatValue());
            String wearCategory = getWearCategoryName(item.getFloatValue());
            System.out.println("磨损值: " + floatText + " (" + wearCategory + ")");
        }

        // 打印印花信息
        if (item.getStickerList() != null && !item.getStickerList().isEmpty()) {
            System.out.println("印花数量: " + item.getStickerList().size());
            for (CommonSticker sticker : item.getStickerList()) {
                System.out.println("  - " + sticker.getItemName() + " (剩余: " + sticker.getWear() + "%)");
            }
        }

        // 打印合并物品的ID列表（如果有）
        if (item.getIds() != null && !item.getIds().isEmpty()) {
            System.out.println("合并物品ID列表: " + item.getIds());
            System.out.println("物品数量: " + item.getQuantity());
        }
    }



    /**
     * 切换物品选中状态
     */
    private void toggleItemSelection(SteamInventoryRes item, VBox itemCard) {
        if (selectedItems.contains(item)) {
            // 取消选中
            selectedItems.remove(item);
            itemCard.getStyleClass().remove("selected-item");
            System.out.println("取消选中物品: " + (item.getItemName() != null ? item.getItemName() : item.getHashName()));
        } else {
            // 选中物品
            selectedItems.add(item);
            itemCard.getStyleClass().add("selected-item");
            System.out.println("选中物品: " + (item.getItemName() != null ? item.getItemName() : item.getHashName()));
        }

        // 更新全选复选框状态
        updateSelectAllCheckbox();

        // 更新按钮状态
        if (currentOnSell) {
            // 在售页面：更新下架按钮状态
            updateUnlistedButtonState();
        } else {
            // 库存页面：更新上架按钮状态
            updateUploadButtonState();
        }
    }

    /**
     * 更新全选复选框状态
     */
    private void updateSelectAllCheckbox() {
        if (currentOnSell) {
            // 在售页面：更新在售全选复选框状态
            if (onSaleSelectAllCheckbox != null) {
                // 计算可选择的物品数量（可交易且在售）
                long selectableItemCount = inventoryItems.stream()
                        .filter(item -> item.getTradable() && item.isOnSell())
                        .count();

                // 如果没有可选择的物品，禁用全选复选框
                if (selectableItemCount == 0) {
                    onSaleSelectAllCheckbox.setDisable(true);
                    onSaleSelectAllCheckbox.setSelected(false);
                    return;
                }

                // 启用全选复选框
                onSaleSelectAllCheckbox.setDisable(false);

                // 计算已选择的可下架物品数量
                long selectedSelectableItemCount = selectedItems.stream()
                        .filter(item -> item.getTradable() && item.isOnSell())
                        .count();

                // 更新全选复选框状态
                onSaleSelectAllCheckbox.setSelected(selectedSelectableItemCount == selectableItemCount);
            }
        } else {
            // 库存页面：更新库存全选复选框状态
            if (selectAllCheckbox != null) {
                // 计算可选择的物品数量（可交易且未在售）
                long selectableItemCount = inventoryItems.stream()
                        .filter(item -> item.getTradable() && !item.isOnSell())
                        .count();

                // 如果没有可选择的物品，禁用全选复选框
                if (selectableItemCount == 0) {
                    selectAllCheckbox.setDisable(true);
                    selectAllCheckbox.setSelected(false);
                    return;
                }

                // 启用全选复选框
                selectAllCheckbox.setDisable(false);

                // 计算已选择的可上架物品数量
                long selectedSelectableItemCount = selectedItems.stream()
                        .filter(item -> item.getTradable() && !item.isOnSell())
                        .count();

                // 更新全选复选框状态
                selectAllCheckbox.setSelected(selectedSelectableItemCount == selectableItemCount);
            }
        }
    }

    /**
     * 更新上架按钮状态
     */
    private void updateUploadButtonState() {
        if (uploadToMarketButton != null) {
            // 如果有选中的可上架物品，启用上架按钮
            boolean hasSelectableItems = selectedItems.stream()
                    .anyMatch(item -> item.getTradable() && !item.isOnSell());

            uploadToMarketButton.setDisable(!hasSelectableItems);
        }
    }

    /**
     * 更新下架按钮状态
     */
    private void updateUnlistedButtonState() {
        if (unlistButton != null) {
            // 如果有选中的在售物品，启用下架按钮
            boolean hasUnmistakableItems = selectedItems.stream()
                    .anyMatch(item -> item.getTradable() && item.isOnSell());

            unlistButton.setDisable(!hasUnmistakableItems);

            // 打印调试信息
            System.out.println("下架按钮状态更新: " + (hasUnmistakableItems ? "启用" : "禁用"));
            System.out.println("选中物品数量: " + selectedItems.size());
            System.out.println("可下架物品数量: " + selectedItems.stream()
                    .filter(item -> item.getTradable() && item.isOnSell())
                    .count());
        }

        // 同时更新修改价格按钮状态
        updateModifyPriceButtonState();
    }

    /**
     * 更新修改价格按钮状态
     */
    private void updateModifyPriceButtonState() {
        if (modifyPriceButton != null) {
            // 如果有选中的在售物品，启用修改价格按钮
            boolean hasModifiableItems = selectedItems.stream()
                    .anyMatch(item -> item.getTradable() && item.isOnSell());

            modifyPriceButton.setDisable(!hasModifiableItems);

            // 打印调试信息
            System.out.println("修改价格按钮状态更新: " + (hasModifiableItems ? "启用" : "禁用"));
            System.out.println("选中物品数量: " + selectedItems.size());
            System.out.println("可修改价格物品数量: " + selectedItems.stream()
                    .filter(item -> item.getTradable() && item.isOnSell())
                    .count());
        }
    }

    /**
     * 处理在售页面全选复选框点击事件
     */
    private void handleOnSaleSelectAllCheckbox() {
        boolean isSelected = onSaleSelectAllCheckbox.isSelected();

        // 清空当前选中的物品
        selectedItems.clear();

        // 遍历库存物品
        for (SteamInventoryRes item : inventoryItems) {
            // 只处理可交易且在售的物品
            if (item.getTradable() && item.isOnSell()) {
                if (isSelected) {
                    // 全选：添加到选中列表
                    selectedItems.add(item);
                }
            }
        }

        // 刷新物品显示，更新选中状态
        refreshInventoryItemsDisplay();

        // 更新下架按钮状态
        updateUnlistedButtonState();
    }

    /**
     * 处理批量修改价格
     */
    private void handleBatchPriceModify() {
        try {
            // 获取选中的可修改价格物品（可交易且在售）
            List<SteamInventoryRes> modifiableItems = selectedItems.stream()
                    .filter(item -> item.getTradable() && item.isOnSell())
                    .collect(Collectors.toList());

            // 打印调试信息
            System.out.println("处理批量修改价格");
            System.out.println("选中物品数量: " + selectedItems.size());
            System.out.println("可修改价格物品数量: " + modifiableItems.size());

            // 如果没有可修改价格的物品，显示提示
            if (modifiableItems.isEmpty()) {
                showAlert(Alert.AlertType.INFORMATION, "提示", "没有选中可修改价格的物品");
                return;
            }

            // 打开修改价格对话框
            openListingDialog(modifiableItems, true);
        } catch (Exception e) {
            System.out.println("打开批量修改价格对话框异常: " + e.getMessage());
            showAlert(Alert.AlertType.ERROR, "错误", "打开修改价格对话框失败: " + e.getMessage());
        }
    }

    /**
     * 处理批量下架
     */
    private void handleBatchUnlisted() {
        try {
            // 获取选中的可下架物品（可交易且在售）
            List<SteamInventoryRes> unmistakableItems = selectedItems.stream()
                    .filter(item -> item.getTradable() && item.isOnSell())
                    .toList();

            // 打印调试信息
            System.out.println("处理批量下架");
            System.out.println("选中物品数量: " + selectedItems.size());
            System.out.println("可下架物品数量: " + unmistakableItems.size());

            // 如果没有可下架的物品，显示提示
            if (unmistakableItems.isEmpty()) {
                showAlert(Alert.AlertType.INFORMATION, "提示", "没有选中可下架的物品");
                return;
            }

            // 创建下架请求（这个request现在只是为了保持原有逻辑，实际会在后面重新构建）
            ChangeOnSellStatusReq request = new ChangeOnSellStatusReq();
            List<ChangeOnSellStatusReq.Inventory> inventoryList = new ArrayList<>();

            // 处理所有选中的物品（用于验证是否有有效物品）
            for (SteamInventoryRes item : unmistakableItems) {
                // 处理合并物品和单个物品的情况
                if (item.getIds() != null && !item.getIds().isEmpty()) {
                    // 合并物品：ids是逗号分隔的字符串，需要拆分
                    String[] idArray = item.getIds().split(",");
                    for (String id : idArray) {
                        if (id != null && !id.trim().isEmpty()) {
                            ChangeOnSellStatusReq.Inventory inventoryItem = new ChangeOnSellStatusReq.Inventory();
                            inventoryItem.setId(Long.parseLong(id.trim()));
                            inventoryItem.setPrice(0); // 价格设为0表示下架
                            inventoryList.add(inventoryItem);
                        }
                    }
                } else if (item.getId() != null) {
                    // 单个物品：使用物品ID
                    ChangeOnSellStatusReq.Inventory inventoryItem = new ChangeOnSellStatusReq.Inventory();
                    inventoryItem.setId(item.getId());
                    inventoryItem.setPrice(0); // 价格设为0表示下架
                    inventoryList.add(inventoryItem);
                }
            }

            if (inventoryList.isEmpty()) {
                showAlert(Alert.AlertType.WARNING, "下架提示", "没有有效的物品可以下架");
                return;
            }

            request.setInventoryList(inventoryList);

            // 禁用下架按钮
            unlistButton.setDisable(true);

            // 在后台线程中发送请求
            new Thread(() -> {
                try {
                    // 分别处理IO661和YouPin的下架
                    List<SteamInventoryRes> io661Items = new ArrayList<>();
                    List<SteamInventoryRes> youPinItems = new ArrayList<>();

                    // 按平台分类物品
                    for (SteamInventoryRes item : unmistakableItems) {
                        if ("youpin".equals(item.getPlatformType())) {
                            youPinItems.add(item);
                        } else {
                            io661Items.add(item);
                        }
                    }

                    // 处理IO661下架
                    final ChangeOnSellStatusRes response;
                    if (!io661Items.isEmpty()) {
                        // 重新构建IO661的下架请求
                        ChangeOnSellStatusReq io661Request = new ChangeOnSellStatusReq();
                        List<ChangeOnSellStatusReq.Inventory> io661InventoryList = new ArrayList<>();

                        for (SteamInventoryRes item : io661Items) {
                            if (item.getIds() != null && !item.getIds().isEmpty()) {
                                String[] idArray = item.getIds().split(",");
                                for (String id : idArray) {
                                    if (id != null && !id.trim().isEmpty()) {
                                        ChangeOnSellStatusReq.Inventory inventoryItem = new ChangeOnSellStatusReq.Inventory();
                                        inventoryItem.setId(Long.parseLong(id.trim()));
                                        inventoryItem.setPrice(0);
                                        io661InventoryList.add(inventoryItem);
                                    }
                                }
                            } else if (item.getId() != null) {
                                ChangeOnSellStatusReq.Inventory inventoryItem = new ChangeOnSellStatusReq.Inventory();
                                inventoryItem.setId(item.getId());
                                inventoryItem.setPrice(0);
                                io661InventoryList.add(inventoryItem);
                            }
                        }

                        if (!io661InventoryList.isEmpty()) {
                            io661Request.setInventoryList(io661InventoryList);
                            response = transactionAssistantService.changeOnSellStatus(io661Request);
                        } else {
                            response = null;
                        }
                    } else {
                        response = null;
                    }

                    // 处理YouPin下架
                    if (!youPinItems.isEmpty()) {
                        // 按steamId分组YouPin物品
                        Map<String, List<SteamInventoryRes>> youPinItemsBySteamId = youPinItems.stream()
                                .collect(Collectors.groupingBy(SteamInventoryRes::getSteamId));

                        for (Map.Entry<String, List<SteamInventoryRes>> entry : youPinItemsBySteamId.entrySet()) {
                            String itemSteamId = entry.getKey();
                            List<SteamInventoryRes> itemsForSteamId = entry.getValue();

                            // 获取对应steamId的YouPin token
                            String youPinToken = readYouPinCookie(itemSteamId);
                            if (youPinToken == null || youPinToken.isEmpty()) {
                                System.out.println("未找到steamId " + itemSteamId + " 的YouPin授权令牌");
                                continue;
                            }

                            // 构建YouPin下架请求
                            YouPinUserItemsOffSaleReq youPinUserItemsOffSaleReq = new YouPinUserItemsOffSaleReq();
                            List<YouPinUserItemsOffSaleReq.Ids> idsList = itemsForSteamId.stream()
                                    .filter(item -> item.getId() != null)
                                    .map(item -> {
                                        YouPinUserItemsOffSaleReq.Ids ids = new YouPinUserItemsOffSaleReq.Ids();
                                        ids.setId(String.valueOf(item.getId())); // 这里的ID是commodityId
                                        return ids;
                                    })
                                    .collect(Collectors.toList());

                            if (!idsList.isEmpty()) {
                                youPinUserItemsOffSaleReq.setIds(idsList);
                                boolean youPinResult = youPinService.userItemsOffSale(youPinToken, youPinUserItemsOffSaleReq);
                                System.out.println("YouPin下架结果 (steamId: " + itemSteamId + "): " +
                                    (youPinResult ? "成功" : "失败") + ", 物品数量: " + idsList.size());
                            }
                        }
                    }

                    // 在JavaFX线程中处理响应
                    Platform.runLater(() -> {
                        int totalItems = unmistakableItems.size();
                        int errorCount = 0;

                        // 计算IO661的错误数量
                        if (response != null && response.getErrorList() != null) {
                            errorCount += response.getErrorList().size();
                        }

                        int successCount = totalItems - errorCount;

                        if (errorCount == 0) {
                            showAlert(Alert.AlertType.INFORMATION, "下架成功",
                                    "成功下架 " + successCount + " 件物品");
                        } else {
                            showAlert(Alert.AlertType.INFORMATION, "下架部分成功",
                                    "成功下架 " + successCount + " 件物品，失败 " + errorCount + " 件物品");
                        }

                        // 刷新库存
                        refreshInventory();
                    });
                } catch (Exception e) {
                    // 处理异常
                    Platform.runLater(() -> {
                        showAlert(Alert.AlertType.ERROR, "下架异常", e.getMessage());
                        unlistButton.setDisable(false);
                    });
                }
            }).start();
        } catch (Exception e) {
            showAlert(Alert.AlertType.ERROR, "下架异常", "处理下架请求时发生错误: " + e.getMessage());
            unlistButton.setDisable(false);
        }
    }

    /**
     * 处理全选复选框点击事件
     */
    private void handleSelectAllCheckbox() {
        boolean isSelected = selectAllCheckbox.isSelected();

        // 清空当前选中的物品
        selectedItems.clear();

        // 遍历库存物品
        for (SteamInventoryRes item : inventoryItems) {
            // 只处理可交易且未在售的物品
            if (item.getTradable() && !item.isOnSell()) {
                if (isSelected) {
                    // 全选：添加到选中列表
                    selectedItems.add(item);
                }
            }
        }

        // 刷新物品显示，更新选中状态
        refreshInventoryItemsDisplay();

        // 更新上架按钮状态
        updateUploadButtonState();
    }

    /**
     * 刷新物品显示，更新选中状态
     */
    private void refreshInventoryItemsDisplay() {
        // 重新渲染物品，以更新选中状态
        renderInventoryItems();
    }

    /**
     * 打开选中物品的上架对话框
     */
    private void openSelectedItemsListingDialog() {
        try {
            // 获取选中的可上架物品（可交易且未在售）
            List<SteamInventoryRes> listableItems = selectedItems.stream()
                    .filter(item -> item.getTradable() && !item.isOnSell())
                    .collect(Collectors.toList());

            // 如果没有可上架的物品，显示提示
            if (listableItems.isEmpty()) {
                showAlert(Alert.AlertType.INFORMATION, "提示", "没有选中可上架的物品");
                return;
            }

            // 打开上架对话框
            openListingDialog(listableItems);
        } catch (Exception e) {
            System.out.println("打开上架对话框异常: " + e.getMessage());
            showAlert(Alert.AlertType.ERROR, "错误", "打开上架对话框失败: " + e.getMessage());
        }
    }



    /**
     * 打开单个物品上架对话框
     */
    private void openSingleItemListingDialog(SteamInventoryRes item) {
        try {
            // 创建只包含一个物品的列表
            List<SteamInventoryRes> singleItemList = new ArrayList<>();
            singleItemList.add(item);

            // 打开上架对话框
            openListingDialog(singleItemList, false);
        } catch (Exception e) {
            System.out.println("打开单个物品上架对话框异常: " + e.getMessage());
            showAlert(Alert.AlertType.ERROR, "错误", "打开上架对话框失败: " + e.getMessage());
        }
    }

    /**
     * 打开单个物品价格修改对话框
     */
    private void openSingleItemPriceModifyDialog(SteamInventoryRes item) {
        try {
            // 创建只包含一个物品的列表
            List<SteamInventoryRes> singleItemList = new ArrayList<>();
            singleItemList.add(item);

            // 打开价格修改对话框
            openListingDialog(singleItemList, true);
        } catch (Exception e) {
            System.out.println("打开单个物品价格修改对话框异常: " + e.getMessage());
            showAlert(Alert.AlertType.ERROR, "错误", "打开价格修改对话框失败: " + e.getMessage());
        }
    }

    /**
     * 打开上架/修改价格对话框
     *
     * @param items 物品列表
     * @param isPriceModify 是否是价格修改模式
     */
    private void openListingDialog(List<SteamInventoryRes> items, boolean isPriceModify) throws Exception {
        // 加载上架对话框FXML
        FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/market-listing-dialog.fxml"));
        Parent root = loader.load();

        // 获取控制器
        MarketListingDialogController controller = loader.getController();

        // 设置对话框标题和按钮文本
        String dialogTitle = isPriceModify ? "修改价格" : "上架市场";
        String confirmButtonText = isPriceModify ? "确定修改" : "确定上架";

        // 设置要上架/修改价格的物品
        controller.setItems(items, isPriceModify, confirmButtonText);

        // 设置当前Steam账号ID
        controller.setCurrentSteamId(currentSteamId);

        // 创建对话框
        Stage dialogStage = new Stage();
        dialogStage.setTitle(dialogTitle);
        dialogStage.initModality(Modality.APPLICATION_MODAL);
        dialogStage.initStyle(StageStyle.UNDECORATED);

        // 设置场景
        Scene scene = new Scene(root);
        dialogStage.setScene(scene);

        // 显示对话框
        dialogStage.showAndWait();

        // 对话框关闭后刷新库存
        refreshInventory();
    }

    /**
     * 打开上架对话框（向后兼容）
     */
    private void openListingDialog(List<SteamInventoryRes> items) throws Exception {
        openListingDialog(items, false);
    }



    /**
     * 显示提示框
     */
    private void showAlert(Alert.AlertType alertType, String title, String content) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }

    /**
     * 设置库存搜索词
     */
    public void setSearchText() {
        String searchText = searchField.getText();
        System.out.println("库存搜索词: " + searchText);
        currentSearch = searchText;
        refreshInventory();
    }

    /**
     * 设置在售搜索词
     */
    public void setOnSaleSearchText() {
        String searchText = onSaleSearchField.getText();
        System.out.println("在售搜索词: " + searchText);
        currentSearch = searchText;
        refreshInventory();
    }

    /**
     * 处理库存搜索框回车事件
     */
    @FXML
    private void handleSearchEnter() {
        setSearchText();
    }

    /**
     * 处理在售搜索框回车事件
     */
    @FXML
    private void handleOnSaleSearchEnter() {
        setOnSaleSearchText();
    }

    /**
     * 加载悠悠有品在售数据
     */
    private List<SteamInventoryRes> loadYouPinOnSellData(String steamId) {
        try {
            // 获取悠悠有品token
            String youPinToken = readYouPinCookie(steamId);
            if (youPinToken == null || youPinToken.isEmpty()) {
                System.out.println("未找到悠悠有品授权令牌");
                return new ArrayList<>();
            }

            // 调用悠悠有品API获取在售数据
            YouPinUserInventoryOnSellDataListRes youPinResponse = youPinService.getUserInventoryOnSellDataList(youPinToken);
            if (youPinResponse == null) {
                System.out.println("获取悠悠有品在售数据失败");
                return new ArrayList<>();
            }

            // 转换数据格式
            return convertYouPinDataToSteamInventoryRes(youPinResponse, steamId);
        } catch (Exception e) {
            System.out.println("加载悠悠有品在售数据异常: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 将悠悠有品数据转换为SteamInventoryRes格式
     */
    private List<SteamInventoryRes> convertYouPinDataToSteamInventoryRes(YouPinUserInventoryOnSellDataListRes youPinResponse, String steamId) {
        List<SteamInventoryRes> result = new ArrayList<>();

        try {
            if (youPinResponse == null || youPinResponse.getData() == null ||
                    youPinResponse.getData().getCommodityInfoList() == null) {
                System.out.println("悠悠有品响应数据为空");
                return result;
            }

            List<YouPinUserInventoryOnSellDataListRes.CommodityInfoList> commodityInfoList =
                    youPinResponse.getData().getCommodityInfoList();

            for (YouPinUserInventoryOnSellDataListRes.CommodityInfoList commodity : commodityInfoList) {
                SteamInventoryRes item = new SteamInventoryRes();

                // 基本信息
                item.setId(commodity.getId());
                item.setItemName(commodity.getName());
                item.setHashName(commodity.getCommodityHashName());
                item.setIconUrl(commodity.getImgUrl());
                item.setAssetId(String.valueOf(commodity.getSteamAssetId()));

                // 数量和状态
                item.setQuantity(commodity.getMergeCommodityCount() != null ? commodity.getMergeCommodityCount() : 1);
                item.setOnSell(true); // 悠悠有品在售数据都是在售状态
                item.setTradable(true); // 假设都可交易

                // 价格信息（悠悠有品价格是字符串格式，需要转换为分）
                String sellAmount = commodity.getSellAmount();
                if (sellAmount != null && !sellAmount.isEmpty()) {
                    try {
                        double price = Double.parseDouble(sellAmount);
                        item.setOnSellPrice((int) Math.round(price * 100)); // 转换为分
                        item.setRefPrice((int) Math.round(price * 100));
                    } catch (NumberFormatException e) {
                        System.out.println("解析价格失败: " + sellAmount);
                    }
                }

                // 磨损值
                String abradeStr = commodity.getAbrade();
                if (abradeStr != null && !abradeStr.isEmpty()) {
                    try {
                        BigDecimal floatValue = new BigDecimal(abradeStr);
                        item.setFloatValue(floatValue);
                    } catch (NumberFormatException e) {
                        System.out.println("解析磨损值失败: " + abradeStr);
                    }
                }

                // 印花信息
                List<YouPinUserInventoryOnSellDataListRes.Stickers> stickers = commodity.getStickers();
                if (stickers != null && !stickers.isEmpty()) {
                    List<CommonSticker> stickerList = new ArrayList<>();
                    for (YouPinUserInventoryOnSellDataListRes.Stickers stickerData : stickers) {
                        CommonSticker sticker = new CommonSticker();
                        sticker.setItemName(stickerData.getName());
                        sticker.setIconUrl(stickerData.getImageUrl());

                        // 磨损度
                        String abradeDesc = stickerData.getAbradeDesc();
                        if (abradeDesc != null && abradeDesc.endsWith("%")) {
                            try {
                                String wearStr = abradeDesc.replace("%", "");
                                sticker.setWear((int) Math.round(Double.parseDouble(wearStr)));
                            } catch (NumberFormatException e) {
                                sticker.setWear(100); // 默认100%
                            }
                        } else {
                            sticker.setWear(100);
                        }

                        stickerList.add(sticker);
                    }
                    item.setStickerList(stickerList);
                }

                // 设置Steam ID（保存真实的steamId，用于后续获取token）
                item.setSteamId(steamId);

                result.add(item);
            }

            System.out.println("成功转换 " + result.size() + " 个悠悠有品在售物品");

        } catch (Exception e) {
            System.out.println("转换悠悠有品数据异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 安全获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;

        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof Integer) {
            return ((Integer) value).longValue();
        } else if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 安全获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> map) {
        Object value = map.get("mergeCommodityCount");
        switch (value) {
            case null -> {
                return 1;
            }
            case Integer i -> {
                return i;
            }
            case Long l -> {
                return l.intValue();
            }
            case String s -> {
                try {
                    return Integer.parseInt((String) value);
                } catch (NumberFormatException e) {
                    return 1;
                }
            }
            default -> {
            }
        }

        return 1;
    }

    /**
     * 检查物品在多个平台的在售状态
     */
    private void checkMultiPlatformOnSellStatus(SteamInventoryRes item) {
        List<String> onSellPlatforms = new ArrayList<>();

        // IO661平台已经确认在售
        if (item.isOnSell()) {
            onSellPlatforms.add("io661");
        }

        try {
            // 检查悠悠有品平台
            if (currentSteamId != null && !currentSteamId.isEmpty()) {
                String youPinToken = readYouPinCookie(currentSteamId);
                if (youPinToken != null && !youPinToken.isEmpty()) {
                    // 检查该物品是否在悠悠有品也在售
                    boolean isOnSellInYouPin = checkItemOnSellInYouPin(item, youPinToken);
                    if (isOnSellInYouPin) {
                        onSellPlatforms.add("youpin");
                    }
                }
            }

            // TODO: 检查BUFF平台
            // boolean isOnSellInBuff = checkItemOnSellInBuff(item, buffToken);
            // if (isOnSellInBuff) {
            //     onSellPlatforms.add("buff");
            // }

        } catch (Exception e) {
            System.out.println("检查多平台在售状态异常: " + e.getMessage());
        }

        // 设置多平台在售状态
        item.setOnSellPlatforms(onSellPlatforms);
    }

    /**
     * 检查物品是否在悠悠有品平台在售
     */
    private boolean checkItemOnSellInYouPin(SteamInventoryRes item, String youPinToken) {
        try {
            // 检查缓存
            String cacheKey = currentSteamId + "_" + youPinToken.hashCode();
            List<YouPinUserInventoryOnSellDataListRes.CommodityInfoList> commodityInfoList = null;

            // 检查缓存是否有效
            if (youPinOnSellCache.containsKey(cacheKey) && youPinCacheTimestamp.containsKey(cacheKey)) {
                long cacheTime = youPinCacheTimestamp.get(cacheKey);
                if (System.currentTimeMillis() - cacheTime < YOUPIN_CACHE_DURATION) {
                    // 使用缓存数据
                    commodityInfoList = youPinOnSellCache.get(cacheKey);
                }
            }

            // 如果缓存无效，重新获取数据
            if (commodityInfoList == null) {
                YouPinUserInventoryOnSellDataListRes youPinResponse = youPinService.getUserInventoryOnSellDataList(youPinToken);
                if (youPinResponse == null || youPinResponse.getData() == null ||
                        youPinResponse.getData().getCommodityInfoList() == null) {
                    return false;
                }

                List<YouPinUserInventoryOnSellDataListRes.CommodityInfoList> newCommodityInfoList =
                        youPinResponse.getData().getCommodityInfoList();

                // 更新缓存
                youPinOnSellCache.put(cacheKey, newCommodityInfoList);
                youPinCacheTimestamp.put(cacheKey, System.currentTimeMillis());
                commodityInfoList = newCommodityInfoList;
            }

            // 通过assetId或hashName匹配物品
            String targetAssetId = item.getAssetId();
            String targetHashName = item.getHashName();

            for (YouPinUserInventoryOnSellDataListRes.CommodityInfoList commodity : commodityInfoList) {
                // 检查assetId匹配
                if (targetAssetId != null) {
                    Long steamAssetId = commodity.getId();
                    if (steamAssetId != null && targetAssetId.equals(String.valueOf(steamAssetId))) {
                        return true;
                    }
                }

                // 检查hashName匹配
                if (targetHashName != null) {
                    String commodityHashName = commodity.getCommodityHashName();
                    if (targetHashName.equals(commodityHashName)) {
                        // 进一步检查磨损值是否匹配（如果有的话）
                        if (item.getFloatValue() != null) {
                            String abradeStr = commodity.getAbrade();
                            if (abradeStr != null) {
                                try {
                                    BigDecimal youPinFloat = new BigDecimal(abradeStr);
                                    // 允许小的浮点数误差
                                    if (item.getFloatValue().subtract(youPinFloat).abs().compareTo(new BigDecimal("0.0001")) < 0) {
                                        return true;
                                    }
                                } catch (NumberFormatException e) {
                                    // 如果解析失败，仅通过hashName匹配
                                    return true;
                                }
                            }
                        } else {
                            // 没有磨损值，仅通过hashName匹配
                            return true;
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("检查悠悠有品在售状态异常: " + e.getMessage());
        }

        return false;
    }
}