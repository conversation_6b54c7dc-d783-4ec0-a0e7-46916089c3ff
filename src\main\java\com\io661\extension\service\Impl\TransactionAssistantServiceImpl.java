package com.io661.extension.service.Impl;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.io661.extension.commonURL.CommonHttpUrl;
import com.io661.extension.model.CommonResult;
import com.io661.extension.model.Steam.*;
import com.io661.extension.model.YouPin.YouPinUserInventoryOnSellDataListRes;
import com.io661.extension.model.YouPin.YouPinUserItemsOffSaleReq;
import com.io661.extension.service.AccountManagerService;
import com.io661.extension.service.TransactionAssistantService;
import com.io661.extension.service.YouPinService;
import com.io661.extension.util.User.UserCookieManager;
import lombok.Data;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.io661.extension.util.YouPin.YouPinCookieManager.readYouPinCookie;

@Data
public class TransactionAssistantServiceImpl implements TransactionAssistantService {
    private final CommonHttpUrl httpClient;
    private final YouPinService youPinService;

    public TransactionAssistantServiceImpl() {
        this.httpClient = new CommonHttpUrl();
        this.youPinService = new com.io661.extension.service.Impl.YouPinServiceImpl();
    }

    @Override
    public List<SteamInventoryRes> getInventoryList(Integer type, String steamId, Integer limit, Integer subType, boolean onSell, Integer sort, String search) {

        String token = UserCookieManager.readToken();
        try {

            httpClient.setAuthToken(token);

            Map<String, String> headers = new HashMap<>();

            headers.put("Cookie", "Authorization=" + token);

            String onSellValue = onSell ? "true" : "false";

            // 处理搜索参数，确保不为null
            String searchParam = search != null ? search : "";

            // 对搜索参数进行URL编码
            String encodedSearch = java.net.URLEncoder.encode(searchParam, StandardCharsets.UTF_8);

            String endpoint = "web/inventory?type=" + type + "&steamId=" + steamId + "&limit=" + limit + "&subType=" + subType + "&onSell=" + onSellValue + "&sort=" + sort;

            // 只有当搜索参数不为空时才添加到URL中
            if (!searchParam.isEmpty()) {
                endpoint += "&search=" + encodedSearch;
            }

            String response = httpClient.doGet(endpoint, null, headers);

            System.out.println("库存接口响应" + response);

            // 使用GsonBuilder注册LocalDateTime适配器
            Gson gson = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter())
                    .create();

            // 解析响应为 SteamInventoryResponse 类型
            com.io661.extension.model.Steam.SteamInventoryResponse inventoryResponse =
                    gson.fromJson(response, com.io661.extension.model.Steam.SteamInventoryResponse.class);

            // 检查响应是否成功
            if (inventoryResponse != null && inventoryResponse.getCode() == 0 &&
                    inventoryResponse.getData() != null && inventoryResponse.getData().getList() != null) {
                return inventoryResponse.getData().getList();
            } else {
                System.out.println("获取库存失败: " + (inventoryResponse != null ? inventoryResponse.getMsg() : "响应为空"));
                return new ArrayList<>();
            }


        }catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return List.of();
    }

    /**
     * 上架物品到市场
     *
     * @param request 上架请求
     * @return 上架响应
     */
    @Override
    public MarketListingResponse listItemsOnMarket(MarketListingRequest request) {
        String token = UserCookieManager.readToken();
        try {
            httpClient.setAuthToken(token);

            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);
            headers.put("Content-Type", "application/json");

            // 将请求对象转换为JSON
            Gson gson = new Gson();
            String jsonBody = gson.toJson(request);

            // 发送POST请求
            String endpoint = "web/inventory/listing";
            String response = httpClient.doPost(endpoint, jsonBody, headers);

            System.out.println("上架接口响应: " + response);

            // 解析响应
            MarketListingResponse listingResponse = gson.fromJson(response, MarketListingResponse.class);

            if (listingResponse != null && listingResponse.getCode() == 0) {
                System.out.println("上架成功: " + (listingResponse.getData() != null ?
                        "成功: " + listingResponse.getData().getSuccessCount() +
                        ", 失败: " + listingResponse.getData().getFailCount() : "无数据"));
            } else {
                System.out.println("上架失败: " + (listingResponse != null ? listingResponse.getMsg() : "响应为空"));
            }

            return listingResponse;
        } catch (Exception e) {
            System.out.println("上架物品异常: " + e.getMessage());

            // 创建一个错误响应
            MarketListingResponse errorResponse = new MarketListingResponse();
            errorResponse.setCode(-1);
            errorResponse.setMsg("上架物品异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 变更物品上下架状态
     *
     * @param request 上下架请求
     * @return 上下架响应
     */
    @Override
    public ChangeOnSellStatusRes changeOnSellStatus(ChangeOnSellStatusReq request) {
        String token = UserCookieManager.readToken();
        try {
            httpClient.setAuthToken(token);

            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);
            headers.put("Content-Type", "application/json");

            // 将请求对象转换为JSON
            Gson gson = new Gson();
            String jsonBody = gson.toJson(request);

            // 打印请求体，用于调试价格问题
            System.out.println("上下架请求体: " + jsonBody);

            // 发送POST请求
            String endpoint = "web/inventory";
            String response = httpClient.doPost(endpoint, jsonBody, headers);

            System.out.println("上下架接口响应: " + response);

            // 解析响应
            CommonResult commonResult =
                gson.fromJson(response, CommonResult.class);

            if (commonResult != null && commonResult.getCode() == 0) {
                System.out.println("IO661上下架操作成功");
                // 从通用响应中提取数据部分
                String dataJson = gson.toJson(commonResult.getData());
                ChangeOnSellStatusRes result = gson.fromJson(dataJson, ChangeOnSellStatusRes.class);

                // 如果是下架操作（价格为null或0），自动下架YouPin对应商品
                autoUnlistYouPinItems(request);

                return result;
            } else {
                System.out.println("上下架操作失败: " + (commonResult != null ? commonResult.getMsg() : "响应为空"));
                return new ChangeOnSellStatusRes();
            }
        } catch (Exception e) {
            System.out.println("上下架操作异常: " + e.getMessage());
            return new ChangeOnSellStatusRes();
        }
    }

    /**
     * 自动下架YouPin对应商品
     * @param request IO661下架请求
     */
    private void autoUnlistYouPinItems(ChangeOnSellStatusReq request) {
        try {
            if (request == null || request.getInventoryList() == null || request.getInventoryList().isEmpty()) {
                return;
            }

            // 检查是否为下架操作（价格为null或0表示下架）
            boolean isUnlistOperation = request.getInventoryList().stream()
                    .anyMatch(item -> item.getPrice() == null || item.getPrice() == 0);

            if (!isUnlistOperation) {
                System.out.println("不是下架操作，跳过YouPin自动下架");
                return;
            }

            System.out.println("检测到IO661下架操作，开始自动下架YouPin对应商品");

            // 获取所有绑定的Steam账户
            List<String> allSteamIds = getAllBoundSteamIds();
            if (allSteamIds.isEmpty()) {
                System.out.println("未找到绑定的Steam账户，跳过YouPin自动下架");
                return;
            }

            // 提取需要下架的物品（价格为null或0的物品）
            List<ChangeOnSellStatusReq.Inventory> itemsToUnlist = request.getInventoryList().stream()
                    .filter(item -> item.getPrice() == null || item.getPrice() == 0)
                    .collect(Collectors.toList());

            if (itemsToUnlist.isEmpty()) {
                System.out.println("没有需要下架的物品");
                return;
            }

            // 为每个Steam账户尝试下架YouPin商品
            for (String steamId : allSteamIds) {
                unlistYouPinItemsForSteamId(steamId, itemsToUnlist);
            }

        } catch (Exception e) {
            System.err.println("自动下架YouPin商品异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 为指定steamId下架YouPin商品
     */
    private void unlistYouPinItemsForSteamId(String steamId, List<ChangeOnSellStatusReq.Inventory> items) {
        try {
            // 获取YouPin token
            String youPinToken = readYouPinCookie(steamId);
            if (youPinToken == null || youPinToken.isEmpty()) {
                System.out.println("未找到steamId=" + steamId + "的YouPin授权令牌，跳过自动下架");
                return;
            }

            // 获取YouPin在售数据
            YouPinUserInventoryOnSellDataListRes onSellData = youPinService.getUserInventoryOnSellDataList(youPinToken);
            if (onSellData == null || onSellData.getData() == null ||
                onSellData.getData().getCommodityInfoList() == null) {
                System.out.println("获取YouPin在售数据失败，steamId=" + steamId);
                return;
            }

            // 根据assetId找到对应的commodityId
            List<String> commodityIds = new ArrayList<>();
            for (ChangeOnSellStatusReq.Inventory item : items) {
                String commodityId = findCommodityIdByAssetId(
                    String.valueOf(item.getAssetId()),
                    onSellData.getData().getCommodityInfoList()
                );
                if (commodityId != null) {
                    commodityIds.add(commodityId);
                }
            }

            if (commodityIds.isEmpty()) {
                System.out.println("未找到需要下架的YouPin商品，steamId=" + steamId);
                return;
            }

            // 构建YouPin下架请求
            YouPinUserItemsOffSaleReq youPinOffSaleReq = new YouPinUserItemsOffSaleReq();
            List<YouPinUserItemsOffSaleReq.Ids> idsList = commodityIds.stream()
                    .map(commodityId -> {
                        YouPinUserItemsOffSaleReq.Ids ids = new YouPinUserItemsOffSaleReq.Ids();
                        ids.setId(commodityId);
                        return ids;
                    })
                    .collect(Collectors.toList());

            youPinOffSaleReq.setIds(idsList);

            // 执行YouPin下架
            boolean youPinResult = youPinService.userItemsOffSale(youPinToken, youPinOffSaleReq);
            System.out.println("YouPin自动下架结果 (steamId: " + steamId + "): " +
                (youPinResult ? "成功" : "失败") + ", 商品数量: " + commodityIds.size());

        } catch (Exception e) {
            System.err.println("下架YouPin商品异常 (steamId: " + steamId + "): " + e.getMessage());
        }
    }

    /**
     * 根据AssetId查找对应的CommodityId
     */
    private String findCommodityIdByAssetId(String assetId, List<YouPinUserInventoryOnSellDataListRes.CommodityInfoList> commodityInfoList) {
        if (assetId == null || commodityInfoList == null) {
            return null;
        }

        for (YouPinUserInventoryOnSellDataListRes.CommodityInfoList commodity : commodityInfoList) {
            // YouPin的steamAssetId与IO661的AssetId是相同的
            if (assetId.equals(commodity.getSteamAssetId())) {
                return String.valueOf(commodity.getId());
            }
        }

        System.out.println("未找到AssetId=" + assetId + "对应的CommodityId");
        return null;
    }

    /**
     * 根据assetId获取对应的steamId
     * 由于无法直接确定assetId属于哪个Steam账户，我们返回所有绑定的Steam账户ID
     * 让调用方为所有账户尝试下架操作
     */
    private List<String> getAllBoundSteamIds() {
        try {
            String token = UserCookieManager.readToken();
            if (token == null || token.isEmpty()) {
                System.out.println("未找到授权令牌，无法获取Steam账户列表");
                return new ArrayList<>();
            }

            // 创建AccountManagerService实例来获取Steam账户列表
            AccountManagerService accountManagerService = new com.io661.extension.service.Impl.AccountManagerServiceImpl();
            List<SteamRes.SteamBind> steamBindList = accountManagerService.getAllSteamAccount(token);

            if (steamBindList == null || steamBindList.isEmpty()) {
                System.out.println("未找到绑定的Steam账户");
                return new ArrayList<>();
            }

            // 提取所有steamId
            return steamBindList.stream()
                    .map(SteamRes.SteamBind::getSteamId)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            System.err.println("获取Steam账户列表异常: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    // LocalDateTime的TypeAdapter
    private static class LocalDateTimeAdapter extends TypeAdapter<LocalDateTime> {
        private final DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

        @Override
        public void write(JsonWriter out, LocalDateTime value) throws IOException {
            if (value == null) {
                out.nullValue();
            } else {
                out.value(formatter.format(value));
            }
        }

        @Override
        public LocalDateTime read(JsonReader in) throws IOException {
            if (in.peek() == com.google.gson.stream.JsonToken.NULL) {
                in.nextNull();
                return null;
            }
            String dateStr = in.nextString();
            if (dateStr == null || dateStr.isEmpty()) {
                return null;
            }
            try {
                return LocalDateTime.parse(dateStr, formatter);
            } catch (Exception e) {
                // 尝试其他格式
                try {
                    return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } catch (Exception ex) {
                    System.out.println("解析日期时间失败: " + dateStr);
                    return null;
                }
            }
        }
    }
}
